from rest_framework import serializers
from .models import Category, Format, Tag, Template, TemplateImage, Review, Favorite
from django.contrib.auth import get_user_model

User = get_user_model()

class CategorySerializer(serializers.ModelSerializer):
    """Serializer for the Category model"""
    template_count = serializers.IntegerField(read_only=True)
    
    class Meta:
        model = Category
        fields = ('id', 'name', 'slug', 'description', 'icon', 'color', 'featured', 'template_count')
        read_only_fields = ('id', 'template_count')

class FormatSerializer(serializers.ModelSerializer):
    """Serializer for the Format model"""
    class Meta:
        model = Format
        fields = ('id', 'name')
        read_only_fields = ('id',)

class TagSerializer(serializers.ModelSerializer):
    """Serializer for the Tag model"""
    class Meta:
        model = Tag
        fields = ('id', 'name', 'slug')
        read_only_fields = ('id',)

class TemplateImageSerializer(serializers.ModelSerializer):
    """Serializer for the TemplateImage model"""
    class Meta:
        model = TemplateImage
        fields = ('id', 'image', 'order', 'alt_text')
        read_only_fields = ('id',)

class TemplateSerializer(serializers.ModelSerializer):
    """Serializer for the Template model"""
    category = CategorySerializer(read_only=True)
    category_id = serializers.PrimaryKeyRelatedField(
        queryset=Category.objects.all(), 
        source='category', 
        write_only=True
    )
    formats = FormatSerializer(many=True, read_only=True)
    format_ids = serializers.PrimaryKeyRelatedField(
        queryset=Format.objects.all(),
        source='formats',
        write_only=True,
        many=True
    )
    tags = TagSerializer(many=True, read_only=True)
    tag_ids = serializers.PrimaryKeyRelatedField(
        queryset=Tag.objects.all(),
        source='tags',
        write_only=True,
        many=True,
        required=False
    )
    images = TemplateImageSerializer(many=True, read_only=True)
    rating = serializers.FloatField(read_only=True)
    review_count = serializers.IntegerField(read_only=True)
    
    class Meta:
        model = Template
        fields = (
            'id', 'title', 'slug', 'category', 'category_id', 'formats', 'format_ids',
            'tags', 'tag_ids', 'description', 'thumbnail', 'template_file', 'features', 'is_premium',
            'canva_url', 'date_added', 'last_modified', 'download_count', 'images',
            'rating', 'review_count'
        )
        read_only_fields = ('id', 'date_added', 'last_modified', 'download_count', 'rating', 'review_count')

class TemplateCreateUpdateSerializer(serializers.ModelSerializer):
    """Serializer for creating and updating Template with nested TemplateImages"""
    images = TemplateImageSerializer(many=True, required=False)
    
    class Meta:
        model = Template
        fields = (
            'id', 'title', 'slug', 'category', 'formats', 'tags', 'description',
            'thumbnail', 'template_file', 'features', 'is_premium', 'canva_url', 'images'
        )
        read_only_fields = ('id',)
    
    def create(self, validated_data):
        images_data = validated_data.pop('images', [])
        formats_data = validated_data.pop('formats', [])
        tags_data = validated_data.pop('tags', [])
        
        template = Template.objects.create(**validated_data)
        
        # Add many-to-many relationships
        if formats_data:
            template.formats.set(formats_data)
        if tags_data:
            template.tags.set(tags_data)
        
        # Create template images
        for image_data in images_data:
            TemplateImage.objects.create(template=template, **image_data)
            
        return template
    
    def update(self, instance, validated_data):
        images_data = validated_data.pop('images', None)
        formats_data = validated_data.pop('formats', None)
        tags_data = validated_data.pop('tags', None)
        
        # Update Template instance
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        
        # Update many-to-many relationships if provided
        if formats_data is not None:
            instance.formats.set(formats_data)
        if tags_data is not None:
            instance.tags.set(tags_data)
        
        # Update template images if provided
        if images_data is not None:
            instance.images.all().delete()  # Remove existing images
            for image_data in images_data:
                TemplateImage.objects.create(template=instance, **image_data)
                
        return instance

class ReviewSerializer(serializers.ModelSerializer):
    """Serializer for the Review model"""
    user = serializers.StringRelatedField(read_only=True)
    
    class Meta:
        model = Review
        fields = ('id', 'template', 'user', 'rating', 'comment', 'created_at', 'updated_at')
        read_only_fields = ('id', 'user', 'created_at', 'updated_at')
    
    def create(self, validated_data):
        # Set the user from the request
        validated_data['user'] = self.context['request'].user
        return super().create(validated_data)

class FavoriteSerializer(serializers.ModelSerializer):
    """Serializer for the Favorite model"""
    user = serializers.StringRelatedField(read_only=True)
    template = TemplateSerializer(read_only=True)
    template_id = serializers.PrimaryKeyRelatedField(
        queryset=Template.objects.all(),
        source='template',
        write_only=True
    )
    
    class Meta:
        model = Favorite
        fields = ('id', 'user', 'template', 'template_id', 'created_at')
        read_only_fields = ('id', 'user', 'created_at')
    
    def create(self, validated_data):
        # Set the user from the request
        validated_data['user'] = self.context['request'].user
        return super().create(validated_data)

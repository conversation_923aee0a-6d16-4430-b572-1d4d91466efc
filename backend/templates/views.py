from rest_framework import viewsets, permissions, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Count, Avg
from .models import Category, Format, Tag, Template, TemplateImage, Review, Favorite
from .serializers import (
    CategorySerializer, FormatSerializer, TagSerializer,
    TemplateSerializer, TemplateCreateUpdateSerializer,
    TemplateImageSerializer, ReviewSerializer, FavoriteSerializer
)

class CategoryViewSet(viewsets.ModelViewSet):
    """ViewSet for viewing and editing Category instances"""
    queryset = Category.objects.all().annotate(template_count=Count('templates'))
    serializer_class = CategorySerializer
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]
    filter_backends = [filters.SearchFilter, DjangoFilterBackend]
    search_fields = ['name', 'description']
    filterset_fields = ['featured']
    lookup_field = 'slug'

class FormatViewSet(viewsets.ModelViewSet):
    """ViewSet for viewing and editing Format instances"""
    queryset = Format.objects.all()
    serializer_class = FormatSerializer
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]

class TagViewSet(viewsets.ModelViewSet):
    """ViewSet for viewing and editing Tag instances"""
    queryset = Tag.objects.all()
    serializer_class = TagSerializer
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]
    lookup_field = 'slug'

class TemplateViewSet(viewsets.ModelViewSet):
    """ViewSet for viewing and editing Template instances"""
    queryset = Template.objects.all().annotate(
        avg_rating=Avg('reviews__rating'),
        review_count=Count('reviews')
    )
    serializer_class = TemplateSerializer
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]
    filter_backends = [filters.SearchFilter, DjangoFilterBackend, filters.OrderingFilter]
    search_fields = ['title', 'description', 'tags__name', 'category__name']
    filterset_fields = ['category', 'formats', 'tags', 'is_premium']
    ordering_fields = ['date_added', 'download_count', 'avg_rating', 'review_count']
    ordering = ['-date_added']
    lookup_field = 'slug'
    
    def get_serializer_class(self):
        if self.action in ['create', 'update', 'partial_update']:
            return TemplateCreateUpdateSerializer
        return TemplateSerializer
    
    @action(detail=True, methods=['post'])
    def increment_downloads(self, request, slug=None):
        """Increment the download count for a template"""
        template = self.get_object()
        template.download_count += 1
        template.save()
        return Response({'status': 'download count incremented'})

class ReviewViewSet(viewsets.ModelViewSet):
    """ViewSet for viewing and editing Review instances"""
    queryset = Review.objects.all()
    serializer_class = ReviewSerializer
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['template', 'user', 'rating']
    
    def get_queryset(self):
        return Review.objects.all()
    
    def perform_create(self, serializer):
        serializer.save(user=self.request.user)

class FavoriteViewSet(viewsets.ModelViewSet):
    """ViewSet for viewing and editing Favorite instances"""
    serializer_class = FavoriteSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['template']
    
    def get_queryset(self):
        return Favorite.objects.filter(user=self.request.user)
    
    def perform_create(self, serializer):
        serializer.save(user=self.request.user)
    
    @action(detail=False, methods=['get'])
    def is_favorite(self, request):
        """Check if a template is favorited by the current user"""
        template_id = request.query_params.get('template_id', None)
        if not template_id:
            return Response({'error': 'template_id is required'}, status=status.HTTP_400_BAD_REQUEST)
            
        is_favorite = Favorite.objects.filter(
            user=request.user,
            template_id=template_id
        ).exists()
        
        return Response({'is_favorite': is_favorite})
